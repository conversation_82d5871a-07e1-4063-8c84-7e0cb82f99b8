#include <windows.h>
#include <commctrl.h>
#include <setupapi.h>
#include <devguid.h>
#include <regstr.h>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <algorithm>
#include <cstring>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "setupapi.lib")

// Window dimensions - Improved layout
#define WINDOW_WIDTH 1000
#define WINDOW_HEIGHT 800

// Control IDs
#define ID_BUTTON_AUTO_CONNECT  1010
#define ID_BUTTON_DISCONNECT    1011
#define ID_STATIC_STATUS        1012
#define ID_PROGRESS_BAR         1013
#define ID_COMBO_PORT_SELECT    1014

// S-Series Commands
#define ID_STATIC_S001          1020
#define ID_EDIT_S001            1021
#define ID_BUTTON_S001          1022


// U-Series Commands
#define ID_STATIC_U001          1030
#define ID_EDIT_U001            1031
#define ID_BUTTON_U001          1032
#define ID_STATIC_U002          1033
#define ID_EDIT_U002            1034
#define ID_BUTTON_U002          1035
#define ID_STATIC_U003          1036
#define ID_COMBO_U003           1037
#define ID_BUTTON_U003          1038
#define ID_STATIC_U004          1039
#define ID_COMBO_U004           1040
#define ID_BUTTON_U004          1041
#define ID_STATIC_U005          1042
#define ID_COMBO_U005_CH        1043
#define ID_COMBO_U005_EN        1044
#define ID_BUTTON_U005          1045
#define ID_STATIC_U006          1046
#define ID_COMBO_U006_CH        1047
#define ID_COMBO_U006_EN        1048
#define ID_BUTTON_U006          1049

// Data display
#define ID_EDIT_SEND            1050
#define ID_BUTTON_SEND          1051
#define ID_EDIT_RECEIVE         1052
#define ID_BUTTON_CLEAR         1053
#define ID_BUTTON_TEST_ALL      1054

class RS485TestApp {
private:
    HWND m_hWnd;
    HWND m_hStaticStatus;
    HWND m_hProgressBar;
    HWND m_hEditSend;
    HWND m_hEditReceive;
    HWND m_hComboPortSelect;
    
    // S-Series controls
    HWND m_hEditS001;      // Slave address input (1-31)
    
    // U-Series controls
    HWND m_hEditU001;      // SEL threshold input (40-500)
    HWND m_hEditU002;      // Max amplitude input (1000-2000)
    HWND m_hComboU003;     // Detection count dropdown (1-5)
    HWND m_hComboU004;     // Power cycle duration dropdown
    HWND m_hComboU005Ch;   // GPIO input channel (0-1)
    HWND m_hComboU005En;   // GPIO input enable (0-1)
    HWND m_hComboU006Ch;   // GPIO output channel (0-1)
    HWND m_hComboU006En;   // GPIO output enable (0-1)
    
    HANDLE m_hComPort;
    bool m_bConnected;
    std::wstring m_currentPort;
    std::vector<std::wstring> m_availablePorts;
    bool m_bInitialized;

public:
    RS485TestApp() : m_hWnd(nullptr), m_hComPort(INVALID_HANDLE_VALUE), 
                     m_bConnected(false), m_bInitialized(false) {}
    
    ~RS485TestApp() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
        }
    }

    bool Initialize(HINSTANCE hInstance) {
        // Initialize common controls first
        INITCOMMONCONTROLSEX icex;
        icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
        icex.dwICC = ICC_PROGRESS_CLASS | ICC_LISTVIEW_CLASSES;
        InitCommonControlsEx(&icex);

        // Register window class - EXACTLY like working version
        WNDCLASSEX wc = {0};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = L"RS485TestWindow";
        wc.cbWndExtra = sizeof(RS485TestApp*);  // CRITICAL: This is the key!

        if (!RegisterClassEx(&wc)) {
            MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK);
            return false;
        }

        // Create main window - Enhanced with parameter inputs
        m_hWnd = CreateWindowEx(
            0,
            L"RS485TestWindow",
            L"RS485 UMDF Driver Test Tool - Updated Layout Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT,
            WINDOW_WIDTH, WINDOW_HEIGHT,
            nullptr, nullptr, hInstance, this  // Pass 'this' as lpParam
        );

        if (!m_hWnd) return false;

        ShowWindow(m_hWnd, SW_SHOW);
        UpdateWindow(m_hWnd);
        return true;
    }

    void CreateControls() {
        if (!m_hWnd) return;

        int yPos = 15;
        const int MARGIN = 20;
        const int LABEL_WIDTH = 120;
        const int INPUT_WIDTH = 80;
        const int BUTTON_WIDTH = 90;
        const int BUTTON_HEIGHT = 28;

        // Connection section with improved layout
        CreateWindow(L"STATIC", L"RS485 Connection Control",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            MARGIN, yPos, 300, 20, m_hWnd, nullptr, nullptr, nullptr);
        yPos += 30;

        // Port selection with proper spacing to avoid overlaps
        CreateWindow(L"STATIC", L"Select Port:", WS_VISIBLE | WS_CHILD,
            MARGIN, yPos, 80, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboPortSelect = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
            MARGIN + 90, yPos, 120, 200, m_hWnd, (HMENU)ID_COMBO_PORT_SELECT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Refresh Ports", WS_VISIBLE | WS_CHILD,
            MARGIN + 220, yPos, 110, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_AUTO_CONNECT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Connect", WS_VISIBLE | WS_CHILD,
            MARGIN + 340, yPos, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_AUTO_CONNECT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
            MARGIN + 440, yPos, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_DISCONNECT, nullptr, nullptr);
        yPos += 40;

        // Status display with better alignment
        CreateWindow(L"STATIC", L"Status:", WS_VISIBLE | WS_CHILD,
            MARGIN, yPos, 60, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hStaticStatus = CreateWindow(L"STATIC", L"Disconnected",
            WS_VISIBLE | WS_CHILD | SS_SUNKEN | SS_LEFT,
            MARGIN + 70, yPos, 400, 22, m_hWnd, (HMENU)ID_STATIC_STATUS, nullptr, nullptr);

        m_hProgressBar = CreateWindow(PROGRESS_CLASS, nullptr, WS_VISIBLE | WS_CHILD,
            MARGIN + 480, yPos, 200, 22, m_hWnd, (HMENU)ID_PROGRESS_BAR, nullptr, nullptr);
        SendMessage(m_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
        yPos += 45;

        // Create command sections with improved spacing
        CreateSystemCommands(yPos);
        CreateUserCommands(yPos + 100);
        CreateDataSection(yPos + 320);

        // Refresh ports on startup
        RefreshPortList();
    }

    void RefreshPortList() {
        if (!m_hComboPortSelect) return;

        // Clear existing items
        SendMessage(m_hComboPortSelect, CB_RESETCONTENT, 0, 0);
        m_availablePorts.clear();

        // Find available COM ports
        m_availablePorts = FindAllCOMPorts();

        // Add ports to dropdown
        for (const auto& port : m_availablePorts) {
            SendMessage(m_hComboPortSelect, CB_ADDSTRING, 0, (LPARAM)port.c_str());
        }

        if (!m_availablePorts.empty()) {
            SendMessage(m_hComboPortSelect, CB_SETCURSEL, 0, 0);
        }

        // Update status
        if (m_availablePorts.empty()) {
            UpdateStatus(L"No COM ports found");
        } else {
            UpdateStatus(L"Found " + std::to_wstring(m_availablePorts.size()) + L" COM port(s)");
        }
    }

    std::vector<std::wstring> FindAllCOMPorts() {
        std::vector<std::wstring> ports;

        // Method 1: Registry enumeration for all COM ports
        HKEY hKey;
        if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, L"HARDWARE\\DEVICEMAP\\SERIALCOMM", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            DWORD index = 0;
            wchar_t valueName[256];
            wchar_t portName[256];
            DWORD valueNameSize, portNameSize;

            while (true) {
                valueNameSize = sizeof(valueName) / sizeof(wchar_t);
                portNameSize = sizeof(portName);

                LONG result = RegEnumValue(hKey, index++, valueName, &valueNameSize, 
                                         nullptr, nullptr, (LPBYTE)portName, &portNameSize);

                if (result == ERROR_NO_MORE_ITEMS) break;
                if (result == ERROR_SUCCESS) {
                    ports.push_back(std::wstring(portName));
                }
            }
            RegCloseKey(hKey);
        }

        return ports;
    }

    void CreateSystemCommands(int startY) {
        const int MARGIN = 20;
        const int LABEL_WIDTH = 280;
        const int INPUT_WIDTH = 80;
        const int BUTTON_WIDTH = 90;
        const int BUTTON_HEIGHT = 28;
        const int ROW_HEIGHT = 35;
        const int SET_BUTTON_X = MARGIN + LABEL_WIDTH + INPUT_WIDTH + 20; // Fixed position for all Set buttons

        // S-Series Commands Section with improved styling
        CreateWindow(L"STATIC", L"System Configuration Commands (S-Series)",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            MARGIN, startY, 500, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 30;

        // S001: Slave Address (1-31) with consistent layout
        CreateWindow(L"STATIC", L"S001 - Set RS485 slave address (1-31):", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, LABEL_WIDTH, 20, m_hWnd, (HMENU)ID_STATIC_S001, nullptr, nullptr);

        m_hEditS001 = CreateWindow(L"EDIT", L"5", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_CENTER,
            MARGIN + LABEL_WIDTH + 10, startY, INPUT_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_EDIT_S001, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            SET_BUTTON_X, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_S001, nullptr, nullptr);
        startY += ROW_HEIGHT;
    }

    void CreateUserCommands(int startY) {
        const int MARGIN = 20;
        const int LABEL_WIDTH = 320;  // Increased width to prevent overlapping
        const int INPUT_WIDTH = 80;
        const int BUTTON_WIDTH = 90;
        const int BUTTON_HEIGHT = 28;
        const int ROW_HEIGHT = 40;    // Increased row height for better spacing
        const int SET_BUTTON_X = MARGIN + LABEL_WIDTH + INPUT_WIDTH + 15; // Fixed position for all Set buttons

        // U-Series Commands Section with improved styling
        CreateWindow(L"STATIC", L"User Configuration Commands (U-Series)",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            MARGIN, startY, 500, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 35;

        // U001: SEL Threshold (40-500 mA) - Split into two lines to prevent overlap
        CreateWindow(L"STATIC", L"U001 - SEL detection threshold", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, LABEL_WIDTH, 20, m_hWnd, (HMENU)ID_STATIC_U001, nullptr, nullptr);
        startY += 20;
        CreateWindow(L"STATIC", L"(40-500 mA):", WS_VISIBLE | WS_CHILD,
            MARGIN + 20, startY, 120, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hEditU001 = CreateWindow(L"EDIT", L"250", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_CENTER,
            MARGIN + 150, startY, INPUT_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_EDIT_U001, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            MARGIN + 250, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_U001, nullptr, nullptr);
        startY += ROW_HEIGHT;

        // U002: Max Amplitude (1000-2000 mA) - Split into two lines to prevent overlap
        CreateWindow(L"STATIC", L"U002 - SEL max amplitude threshold", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, LABEL_WIDTH, 20, m_hWnd, (HMENU)ID_STATIC_U002, nullptr, nullptr);
        startY += 20;
        CreateWindow(L"STATIC", L"(1000-2000 mA):", WS_VISIBLE | WS_CHILD,
            MARGIN + 20, startY, 120, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hEditU002 = CreateWindow(L"EDIT", L"1500", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_CENTER,
            MARGIN + 150, startY, INPUT_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_EDIT_U002, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            MARGIN + 250, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_U002, nullptr, nullptr);
        startY += ROW_HEIGHT;

        // U003: Detection Count (1-5) with consistent layout
        CreateWindow(L"STATIC", L"U003 - Number of SEL detections before power cycle (1-5):", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, LABEL_WIDTH, 20, m_hWnd, (HMENU)ID_STATIC_U003, nullptr, nullptr);

        m_hComboU003 = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            MARGIN + 150, startY, INPUT_WIDTH, 150, m_hWnd, (HMENU)ID_COMBO_U003, nullptr, nullptr);

        for (int i = 1; i <= 5; i++) {
            wchar_t buffer[10];
            swprintf_s(buffer, L"%d", i);
            SendMessage(m_hComboU003, CB_ADDSTRING, 0, (LPARAM)buffer);
        }
        SendMessage(m_hComboU003, CB_SETCURSEL, 2, 0); // Default to 3

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            MARGIN + 250, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_U003, nullptr, nullptr);
        startY += ROW_HEIGHT;

        // U004: Power Cycle Duration with consistent layout
        CreateWindow(L"STATIC", L"U004 - Power cycle duration (ms):", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, LABEL_WIDTH, 20, m_hWnd, (HMENU)ID_STATIC_U004, nullptr, nullptr);

        m_hComboU004 = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            MARGIN + 150, startY, INPUT_WIDTH, 150, m_hWnd, (HMENU)ID_COMBO_U004, nullptr, nullptr);

        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"200");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"400");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"600");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"800");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"1000");
        SendMessage(m_hComboU004, CB_SETCURSEL, 2, 0); // Default to 600

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            MARGIN + 250, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_U004, nullptr, nullptr);
        startY += ROW_HEIGHT;

        // U005: GPIO Input with improved layout - fully visible labels and controls
        CreateWindow(L"STATIC", L"U005 - Enable/disable GPIO input functions:", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, 280, 20, m_hWnd, (HMENU)ID_STATIC_U005, nullptr, nullptr);
        startY += 25; // Move controls to next line for better visibility

        CreateWindow(L"STATIC", L"Channel:", WS_VISIBLE | WS_CHILD,
            MARGIN + 20, startY, 60, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU005Ch = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            MARGIN + 85, startY, 50, 100, m_hWnd, (HMENU)ID_COMBO_U005_CH, nullptr, nullptr);
        SendMessage(m_hComboU005Ch, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hComboU005Ch, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU005Ch, CB_SETCURSEL, 0, 0);

        CreateWindow(L"STATIC", L"Enable:", WS_VISIBLE | WS_CHILD,
            MARGIN + 150, startY, 50, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU005En = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            MARGIN + 205, startY, 80, 100, m_hWnd, (HMENU)ID_COMBO_U005_EN, nullptr, nullptr);
        SendMessage(m_hComboU005En, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hComboU005En, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hComboU005En, CB_SETCURSEL, 1, 0);

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            MARGIN + 300, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_U005, nullptr, nullptr);
        startY += ROW_HEIGHT;

        // U006: GPIO Output with improved layout - fully visible labels and controls
        CreateWindow(L"STATIC", L"U006 - Enable/disable GPIO output functions:", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, 280, 20, m_hWnd, (HMENU)ID_STATIC_U006, nullptr, nullptr);
        startY += 25; // Move controls to next line for better visibility

        CreateWindow(L"STATIC", L"Channel:", WS_VISIBLE | WS_CHILD,
            MARGIN + 20, startY, 60, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU006Ch = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            MARGIN + 85, startY, 50, 100, m_hWnd, (HMENU)ID_COMBO_U006_CH, nullptr, nullptr);
        SendMessage(m_hComboU006Ch, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hComboU006Ch, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU006Ch, CB_SETCURSEL, 1, 0);

        CreateWindow(L"STATIC", L"Enable:", WS_VISIBLE | WS_CHILD,
            MARGIN + 150, startY, 50, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU006En = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            MARGIN + 205, startY, 80, 100, m_hWnd, (HMENU)ID_COMBO_U006_EN, nullptr, nullptr);
        SendMessage(m_hComboU006En, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hComboU006En, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hComboU006En, CB_SETCURSEL, 1, 0);

        CreateWindow(L"BUTTON", L"Set", WS_VISIBLE | WS_CHILD,
            MARGIN + 300, startY, BUTTON_WIDTH, BUTTON_HEIGHT, m_hWnd, (HMENU)ID_BUTTON_U006, nullptr, nullptr);
    }

    void CreateDataSection(int startY) {
        const int MARGIN = 20;
        const int BUTTON_HEIGHT = 32;
        const int WINDOW_CENTER = 400; // Approximate center of window

        // Execute section with properly centered button and increased spacing
        startY += 25; // Increased space before the button for better separation from U006
        CreateWindow(L"BUTTON", L"Execute All Commands", WS_VISIBLE | WS_CHILD,
            WINDOW_CENTER - 90, startY, 180, BUTTON_HEIGHT + 8, m_hWnd, (HMENU)ID_BUTTON_TEST_ALL, nullptr, nullptr);
        startY += 70; // More space after the button

        // Send data section with improved layout and explanation
        CreateWindow(L"STATIC", L"Send Data (Hex):", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, 120, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 20;

        // Add explanation text for Send Data section
        CreateWindow(L"STATIC", L"Use this section to send raw hexadecimal data directly to the RS485 device for testing and debugging.", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, 700, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 25;

        m_hEditSend = CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
            MARGIN, startY, 720, 28, m_hWnd, (HMENU)ID_EDIT_SEND, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Send", WS_VISIBLE | WS_CHILD,
            MARGIN + 730, startY, 80, 28, m_hWnd, (HMENU)ID_BUTTON_SEND, nullptr, nullptr);
        startY += 40;

        // Receive data section with improved layout
        CreateWindow(L"STATIC", L"Received Data & Results:", WS_VISIBLE | WS_CHILD,
            MARGIN, startY, 200, 20, m_hWnd, nullptr, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Clear", WS_VISIBLE | WS_CHILD,
            MARGIN + 730, startY, 80, 28, m_hWnd, (HMENU)ID_BUTTON_CLEAR, nullptr, nullptr);
        startY += 25;

        m_hEditReceive = CreateWindow(L"EDIT", nullptr,
            WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
            MARGIN, startY, 810, 180, m_hWnd, (HMENU)ID_EDIT_RECEIVE, nullptr, nullptr);
    }

    bool ConnectToSelectedPort() {
        if (!m_hComboPortSelect) return false;

        int sel = SendMessage(m_hComboPortSelect, CB_GETCURSEL, 0, 0);
        if (sel == CB_ERR || sel >= (int)m_availablePorts.size()) {
            AppendToReceiveBox(L"❌ No port selected");
            return false;
        }

        std::wstring selectedPort = m_availablePorts[sel];
        AppendToReceiveBox(L"Attempting to connect to " + selectedPort + L"...");

        if (ConnectToPort(selectedPort)) {
            AppendToReceiveBox(L"✓ Successfully connected to " + selectedPort);
            UpdateStatus(L"Connected to " + selectedPort + L" - Ready for testing");
            return true;
        } else {
            AppendToReceiveBox(L"❌ Failed to connect to " + selectedPort);
            UpdateStatus(L"Connection failed - Check device and drivers");
            return false;
        }
    }

    bool ConnectToPort(const std::wstring& portName) {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }

        std::wstring fullPortName = L"\\\\.\\" + portName;

        m_hComPort = CreateFile(
            fullPortName.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );

        if (m_hComPort == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            AppendToReceiveBox(L"CreateFile failed with error: " + std::to_wstring(error));
            return false;
        }

        // Configure port settings for RS485
        DCB dcb = {0};
        dcb.DCBlength = sizeof(DCB);

        if (!GetCommState(m_hComPort, &dcb)) {
            AppendToReceiveBox(L"GetCommState failed");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        dcb.BaudRate = CBR_9600;    // Fixed baud rate at 9600 (requirement)
        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;
        dcb.fBinary = TRUE;
        dcb.fParity = FALSE;
        dcb.fOutxCtsFlow = FALSE;
        dcb.fOutxDsrFlow = FALSE;
        dcb.fDtrControl = DTR_CONTROL_DISABLE;
        dcb.fDsrSensitivity = FALSE;
        dcb.fTXContinueOnXoff = FALSE;
        dcb.fOutX = FALSE;
        dcb.fInX = FALSE;
        dcb.fErrorChar = FALSE;
        dcb.fNull = FALSE;
        dcb.fRtsControl = RTS_CONTROL_DISABLE;
        dcb.fAbortOnError = FALSE;

        if (!SetCommState(m_hComPort, &dcb)) {
            AppendToReceiveBox(L"SetCommState failed");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        // Set timeouts
        COMMTIMEOUTS timeouts = {0};
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 100;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = 100;
        timeouts.WriteTotalTimeoutMultiplier = 10;

        if (!SetCommTimeouts(m_hComPort, &timeouts)) {
            AppendToReceiveBox(L"SetCommTimeouts failed");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        // Test communication with RS485 device by sending a simple query
        if (!TestRS485Communication()) {
            AppendToReceiveBox(L"❌ RS485 device not responding on " + portName);
            AppendToReceiveBox(L"   Make sure the RS485 device is connected and powered on");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        m_bConnected = true;
        m_currentPort = portName;
        AppendToReceiveBox(L"✓ RS485 device communication verified");
        return true;
    }

    // Test RS485 device communication by sending a simple status query
    bool TestRS485Communication() {
        if (m_hComPort == INVALID_HANDLE_VALUE) {
            return false;
        }

        // Create a simple test frame to query device status (A002 command)
        BYTE testFrame[16] = {
            0xAA,           // Header (1 byte)
            0xE1,           // ID byte (1 byte)
            'A', '0', '0', '2', // Command: A002 (4 bytes)
            0x00, 0x00, 0x00, 0x00, // Value (4 bytes)
            0x00, 0x00, 0x00, 0x00, // Padding (4 bytes) - total payload = 12 bytes
            0x00,           // CRC low byte (1 byte)
            0x0D            // Trailer (1 byte) - total = 16 bytes
        };

        DWORD bytesWritten = 0;
        if (!WriteFile(m_hComPort, testFrame, sizeof(testFrame), &bytesWritten, nullptr)) {
            AppendToReceiveBox(L"Failed to send test frame");
            return false;
        }

        if (bytesWritten != sizeof(testFrame)) {
            AppendToReceiveBox(L"Incomplete test frame sent");
            return false;
        }

        // Wait for response (simplified - in real implementation would be more sophisticated)
        Sleep(100);

        BYTE responseBuffer[32];
        DWORD bytesRead = 0;
        if (ReadFile(m_hComPort, responseBuffer, sizeof(responseBuffer), &bytesRead, nullptr)) {
            if (bytesRead > 0) {
                // Check if we got a valid response frame
                if (bytesRead >= 16 && responseBuffer[0] == 0xAA && responseBuffer[15] == 0x0D) {
                    AppendToReceiveBox(L"✓ Valid RS485 response received (" + std::to_wstring(bytesRead) + L" bytes)");
                    return true;
                } else {
                    AppendToReceiveBox(L"⚠ Received data but not valid RS485 frame format");
                    // Still consider this a successful connection since device is responding
                    return true;
                }
            }
        }

        // No response received - could be device not present or not responding
        AppendToReceiveBox(L"⚠ No response from RS485 device (timeout)");
        return false;
    }

    void Disconnect() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }
        m_bConnected = false;
        m_currentPort.clear();
        UpdateStatus(L"Disconnected");
        AppendToReceiveBox(L"Disconnected from RS485 port");
    }

    void UpdateStatus(const std::wstring& status) {
        if (m_hStaticStatus) {
            SetWindowText(m_hStaticStatus, status.c_str());
        }
    }

    void AppendToReceiveBox(const std::wstring& text) {
        if (!m_hEditReceive) return;

        // Get current text length
        int textLength = GetWindowTextLength(m_hEditReceive);

        // Move cursor to end
        SendMessage(m_hEditReceive, EM_SETSEL, textLength, textLength);

        // Add timestamp
        SYSTEMTIME st;
        GetLocalTime(&st);
        wchar_t timestamp[64];
        swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

        std::wstring fullText = timestamp + text + L"\r\n";

        // Append text
        SendMessage(m_hEditReceive, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());

        // Scroll to bottom
        SendMessage(m_hEditReceive, EM_SCROLLCARET, 0, 0);
    }

    void SendCommand(const std::string& command, UINT32 value) {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected to RS485 port");
            return;
        }

        AppendToReceiveBox(L"Sending " + std::wstring(command.begin(), command.end()) +
                          L" command with value: " + std::to_wstring(value));

        // Create RS485 frame according to protocol
        BYTE frame[16] = {0};
        frame[0] = 0xAA;  // Header
        frame[1] = 0xE1;  // ID byte: function code 0b111 (assign) + slave address 1

        // Command key (4 bytes) - store in payload bytes 2-5
        size_t cmdLen = command.length();
        if (cmdLen > 4) cmdLen = 4;
        memcpy(&frame[2], command.c_str(), cmdLen);

        // Value (4 bytes, little-endian) - store in payload bytes 6-9
        frame[6] = (BYTE)(value & 0xFF);
        frame[7] = (BYTE)((value >> 8) & 0xFF);
        frame[8] = (BYTE)((value >> 16) & 0xFF);
        frame[9] = (BYTE)((value >> 24) & 0xFF);

        // Padding (4 bytes) - payload bytes 10-13
        frame[10] = frame[11] = frame[12] = frame[13] = 0x00;

        // CRC8 (placeholder for now)
        frame[14] = 0x00;

        // Trailer
        frame[15] = 0x0D;

        // Display hex data in send box
        std::wstringstream hexStream;
        for (int i = 0; i < 16; i++) {
            hexStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << frame[i];
            if (i < 15) hexStream << L" ";
        }
        SetWindowText(m_hEditSend, hexStream.str().c_str());

        // Send data with error handling
        DWORD bytesWritten;
        if (WriteFile(m_hComPort, frame, 16, &bytesWritten, nullptr)) {
            AppendToReceiveBox(L"✓ Command sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");

            // Try to read response with timeout
            BYTE response[16] = {0};
            DWORD bytesRead;

            // Wait a bit for response
            std::this_thread::sleep_for(std::chrono::milliseconds(50));

            if (ReadFile(m_hComPort, response, 16, &bytesRead, nullptr) && bytesRead > 0) {
                std::wstringstream responseStream;
                responseStream << L"Response (" << bytesRead << L" bytes): ";
                for (DWORD i = 0; i < bytesRead; i++) {
                    responseStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << response[i];
                    if (i < bytesRead - 1) responseStream << L" ";
                }
                AppendToReceiveBox(responseStream.str());
            } else {
                AppendToReceiveBox(L"No response received (normal for testing without FPGA)");
            }
        } else {
            DWORD error = GetLastError();
            AppendToReceiveBox(L"❌ Failed to send command (Error: " + std::to_wstring(error) + L")");
        }
    }

    void SendGPIOCommand(const std::string& command, UINT32 channel, UINT32 enable) {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected to RS485 port");
            return;
        }

        AppendToReceiveBox(L"Sending " + std::wstring(command.begin(), command.end()) +
                          L" command - Channel: " + std::to_wstring(channel) +
                          L", Enable: " + std::to_wstring(enable));

        // Create RS485 frame with dual integer format for GPIO
        BYTE frame[16] = {0};
        frame[0] = 0xAA;  // Header
        frame[1] = 0xE1;  // ID byte: function code 0b111 (assign) + slave address 1

        // Command key (4 bytes)
        size_t cmdLen2 = command.length();
        if (cmdLen2 > 4) cmdLen2 = 4;
        memcpy(&frame[2], command.c_str(), cmdLen2);

        // Channel (lower 32 bits) - bytes 6-9
        frame[6] = (BYTE)(channel & 0xFF);
        frame[7] = (BYTE)((channel >> 8) & 0xFF);
        frame[8] = (BYTE)((channel >> 16) & 0xFF);
        frame[9] = (BYTE)((channel >> 24) & 0xFF);

        // Enable flag (upper 32 bits) - bytes 10-13
        frame[10] = (BYTE)(enable & 0xFF);
        frame[11] = (BYTE)((enable >> 8) & 0xFF);
        frame[12] = (BYTE)((enable >> 16) & 0xFF);
        frame[13] = (BYTE)((enable >> 24) & 0xFF);

        // CRC8 (placeholder)
        frame[14] = 0x00;

        // Trailer
        frame[15] = 0x0D;

        // Display and send
        std::wstringstream hexStream;
        for (int i = 0; i < 16; i++) {
            hexStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << frame[i];
            if (i < 15) hexStream << L" ";
        }
        SetWindowText(m_hEditSend, hexStream.str().c_str());

        DWORD bytesWritten;
        if (WriteFile(m_hComPort, frame, 16, &bytesWritten, nullptr)) {
            AppendToReceiveBox(L"✓ GPIO command sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");

            // Try to read response
            BYTE response[16] = {0};
            DWORD bytesRead;
            std::this_thread::sleep_for(std::chrono::milliseconds(50));

            if (ReadFile(m_hComPort, response, 16, &bytesRead, nullptr) && bytesRead > 0) {
                std::wstringstream responseStream;
                responseStream << L"Response: ";
                for (DWORD i = 0; i < bytesRead; i++) {
                    responseStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << response[i];
                    if (i < bytesRead - 1) responseStream << L" ";
                }
                AppendToReceiveBox(responseStream.str());
            } else {
                AppendToReceiveBox(L"No response received (normal for testing without FPGA)");
            }
        } else {
            DWORD error = GetLastError();
            AppendToReceiveBox(L"❌ Failed to send GPIO command (Error: " + std::to_wstring(error) + L")");
        }
    }

    void TestAllCommands() {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected - please connect first");
            return;
        }

        AppendToReceiveBox(L"=== Testing All Commands ===");

        // Test S-series commands
        AppendToReceiveBox(L"Testing S-series (System Configuration) commands...");
        SendCommand("S001", 5);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("S002", 115200);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));

        // Test U-series commands
        AppendToReceiveBox(L"Testing U-series (User Configuration) commands...");
        SendCommand("U001", 250);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("U002", 1500);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("U003", 3);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("U004", 600);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendGPIOCommand("U005", 0, 1);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendGPIOCommand("U006", 1, 1);

        AppendToReceiveBox(L"=== All Commands Test Completed ===");
    }

    void SendRawData() {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected to RS485 port");
            return;
        }

        wchar_t buffer[1024];
        GetWindowText(m_hEditSend, buffer, 1024);

        std::wstring hexData(buffer);
        if (hexData.empty()) {
            AppendToReceiveBox(L"❌ No data to send");
            return;
        }

        AppendToReceiveBox(L"Sending raw data: " + hexData);

        // Parse hex string and send
        std::vector<BYTE> data;
        std::wstringstream ss(hexData);
        std::wstring hexByte;

        while (ss >> hexByte) {
            if (hexByte.length() == 2) {
                try {
                    BYTE byte = (BYTE)std::stoul(hexByte, nullptr, 16);
                    data.push_back(byte);
                } catch (...) {
                    AppendToReceiveBox(L"❌ Invalid hex data format");
                    return;
                }
            }
        }

        if (!data.empty()) {
            DWORD bytesWritten;
            if (WriteFile(m_hComPort, data.data(), (DWORD)data.size(), &bytesWritten, nullptr)) {
                AppendToReceiveBox(L"✓ Raw data sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");
            } else {
                AppendToReceiveBox(L"❌ Failed to send raw data");
            }
        }
    }

    // Window procedure - EXACTLY like working version
    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        RS485TestApp* pApp = nullptr;

        if (uMsg == WM_CREATE) {
            CREATESTRUCT* pCreate = (CREATESTRUCT*)lParam;
            pApp = (RS485TestApp*)pCreate->lpCreateParams;
            SetWindowLongPtr(hWnd, 0, (LONG_PTR)pApp);  // Store at offset 0, not GWLP_USERDATA
            pApp->m_hWnd = hWnd;
            pApp->CreateControls();
            pApp->AppendToReceiveBox(L"RS485 UMDF Driver Test Tool - Updated Layout Version");
            pApp->AppendToReceiveBox(L"Application started successfully");
            pApp->AppendToReceiveBox(L"");
            pApp->AppendToReceiveBox(L"Available Commands:");
            pApp->AppendToReceiveBox(L"  S001: Set slave address (1-31)");
            pApp->AppendToReceiveBox(L"  U001: SEL threshold (40-500 mA)");
            pApp->AppendToReceiveBox(L"  U002: SEL max amplitude (1000-2000 mA)");
            pApp->AppendToReceiveBox(L"  U003: Detection count (1-5)");
            pApp->AppendToReceiveBox(L"  U004: Power cycle duration (200-1000 ms)");
            pApp->AppendToReceiveBox(L"  U005: GPIO input functions");
            pApp->AppendToReceiveBox(L"  U006: GPIO output functions");
            pApp->AppendToReceiveBox(L"");
            pApp->AppendToReceiveBox(L"Click 'Refresh Ports' to scan for available COM ports");
        } else {
            pApp = (RS485TestApp*)GetWindowLongPtr(hWnd, 0);  // Get from offset 0
        }

        if (pApp) {
            switch (uMsg) {
            case WM_COMMAND:
                pApp->HandleCommand(LOWORD(wParam));
                return 0;
            case WM_DESTROY:
                pApp->Disconnect();
                PostQuitMessage(0);
                return 0;
            }
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }



    void HandleCommand(WORD commandId) {
        try {
            switch (commandId) {
            case ID_BUTTON_AUTO_CONNECT:
                RefreshPortList();
                if (!ConnectToSelectedPort()) {
                    AppendToReceiveBox(L"Connection failed. Please check:");
                    AppendToReceiveBox(L"1. RS485 device is connected");
                    AppendToReceiveBox(L"2. Correct COM port is selected");
                    AppendToReceiveBox(L"3. No other application is using the port");
                }
                break;

            case ID_BUTTON_DISCONNECT:
                Disconnect();
                break;

            case ID_BUTTON_S001: {
                wchar_t buffer[32];
                GetWindowText(m_hEditS001, buffer, 32);
                int value = _wtoi(buffer);
                if (value >= 1 && value <= 31) {
                    SendCommand("S001", value);
                } else {
                    AppendToReceiveBox(L"❌ S001 value must be between 1-31");
                }
                break;
            }



            case ID_BUTTON_U001: {
                wchar_t buffer[32];
                GetWindowText(m_hEditU001, buffer, 32);
                int value = _wtoi(buffer);
                if (value >= 40 && value <= 500) {
                    SendCommand("U001", value);
                } else {
                    AppendToReceiveBox(L"❌ U001 value must be between 40-500 mA");
                }
                break;
            }

            case ID_BUTTON_U002: {
                wchar_t buffer[32];
                GetWindowText(m_hEditU002, buffer, 32);
                int value = _wtoi(buffer);
                if (value >= 1000 && value <= 2000) {
                    SendCommand("U002", value);
                } else {
                    AppendToReceiveBox(L"❌ U002 value must be between 1000-2000 mA");
                }
                break;
            }

            case ID_BUTTON_U003: {
                int sel = SendMessage(m_hComboU003, CB_GETCURSEL, 0, 0);
                if (sel != CB_ERR) {
                    SendCommand("U003", sel + 1); // 1-5
                } else {
                    AppendToReceiveBox(L"❌ Please select detection count");
                }
                break;
            }

            case ID_BUTTON_U004: {
                int sel = SendMessage(m_hComboU004, CB_GETCURSEL, 0, 0);
                if (sel != CB_ERR) {
                    UINT32 durations[] = {200, 400, 600, 800, 1000};
                    SendCommand("U004", durations[sel]);
                } else {
                    AppendToReceiveBox(L"❌ Please select power cycle duration");
                }
                break;
            }

            case ID_BUTTON_U005: {
                int channel = SendMessage(m_hComboU005Ch, CB_GETCURSEL, 0, 0);
                int enable = SendMessage(m_hComboU005En, CB_GETCURSEL, 0, 0);
                if (channel != CB_ERR && enable != CB_ERR) {
                    SendGPIOCommand("U005", channel, enable);
                } else {
                    AppendToReceiveBox(L"❌ Please select channel and enable/disable for U005");
                }
                break;
            }

            case ID_BUTTON_U006: {
                int channel = SendMessage(m_hComboU006Ch, CB_GETCURSEL, 0, 0);
                int enable = SendMessage(m_hComboU006En, CB_GETCURSEL, 0, 0);
                if (channel != CB_ERR && enable != CB_ERR) {
                    SendGPIOCommand("U006", channel, enable);
                } else {
                    AppendToReceiveBox(L"❌ Please select channel and enable/disable for U006");
                }
                break;
            }

            case ID_BUTTON_TEST_ALL:
                TestAllCommands();
                break;

            case ID_BUTTON_CLEAR:
                SetWindowText(m_hEditReceive, L"");
                AppendToReceiveBox(L"Display cleared - Ready for new tests");
                break;

            case ID_BUTTON_SEND:
                SendRawData();
                break;

            default:
                break;
            }
        } catch (const std::exception& e) {
            std::string errorMsg = "Exception in HandleCommand: " + std::string(e.what());
            AppendToReceiveBox(std::wstring(errorMsg.begin(), errorMsg.end()));
        } catch (...) {
            AppendToReceiveBox(L"Unknown exception in HandleCommand");
        }
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // CRITICAL: Initialize common controls - exactly like working version
    InitCommonControls();

    RS485TestApp app;
    if (!app.Initialize(hInstance)) {
        MessageBox(nullptr, L"Failed to create window!", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
