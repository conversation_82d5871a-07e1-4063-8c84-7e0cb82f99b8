//
// RS485IOCTLHandlers.cpp - IOCTL request handlers
// RS485 UMDF 2.0 Driver for AI-SLDAP Communication
//

#include "RS485FilterDriver.h"

//
// Handle data request IOCTL
//
NTSTATUS RS485HandleDataRequestIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID inputBuffer = NULL;
    PVOID outputBuffer = NULL;

    RS485_DEBUG_PRINT("Handling data request IOCTL");

    // Get input buffer (contains request data)
    if (inputBufferLength > 0) {
        status = WdfRequestRetrieveInputBuffer(request, inputBufferLength, &inputBuffer, NULL);
        if (!NT_SUCCESS(status)) {
            RS485_DEBUG_PRINT("Failed to retrieve input buffer: 0x%08X", status);
            return status;
        }
    }

    // Get output buffer (for response data)
    if (outputBufferLength > 0) {
        status = WdfRequestRetrieveOutputBuffer(request, outputBufferLength, &outputBuffer, NULL);
        if (!NT_SUCCESS(status)) {
            RS485_DEBUG_PRINT("Failed to retrieve output buffer: 0x%08X", status);
            return status;
        }
    }

    // Process the data request
    if (inputBuffer != NULL && inputBufferLength >= RS485_PAYLOAD_SIZE) {
        // Create frame from input data
        RS485_FRAME frame = {0};
        frame.Header = RS485_FRAME_HEADER;
        frame.SlaveId = deviceContext->CurrentSlaveAddress;
        RtlCopyMemory(frame.Payload, inputBuffer, RS485_PAYLOAD_SIZE);
        frame.Trailer = RS485_FRAME_TRAILER;

        // Calculate CRC (simplified implementation)
        frame.Crc = RS485CalculateFrameCRC(&frame);

        // Store frame in uplink buffer for transmission using proper buffer functions
        status = RS485PushPayload(deviceContext->UplinkBuffer, frame.Payload);
        if (!NT_SUCCESS(status)) {
            RS485_DEBUG_PRINT("Failed to push payload to uplink buffer: 0x%08X", status);
        }
        if (!NT_SUCCESS(status)) {
            RS485HandleBufferOverflow(deviceContext, BufferTypeUplink, frame.Payload);
            return status;
        }

        // Update statistics
        deviceContext->HardwareStatus.TotalFramesSent++;
    }

    // Complete the request
    WdfRequestCompleteWithInformation(request, status, 0);
    return status;
}

//
// Handle system configuration IOCTL (S001 command)
//
NTSTATUS RS485HandleSystemConfigIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID inputBuffer = NULL;
    PRS485_SYSTEM_CONFIG_INPUT configInput = NULL;

    RS485_DEBUG_PRINT("Handling system configuration IOCTL");

    // Validate input buffer size
    if (inputBufferLength < sizeof(RS485_SYSTEM_CONFIG_INPUT)) {
        WdfRequestComplete(request, STATUS_BUFFER_TOO_SMALL);
        return STATUS_BUFFER_TOO_SMALL;
    }

    // Get input buffer
    status = WdfRequestRetrieveInputBuffer(request, inputBufferLength, &inputBuffer, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to retrieve input buffer: 0x%08X", status);
        WdfRequestComplete(request, status);
        return status;
    }

    configInput = (PRS485_SYSTEM_CONFIG_INPUT)inputBuffer;

    // Validate command key
    if (!RS485_IS_SYSTEM_COMMAND(configInput->CommandKey)) {
        RS485_DEBUG_PRINT("Invalid system command key: %.4s", configInput->CommandKey);
        WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
        return STATUS_INVALID_PARAMETER;
    }

    // Create frame for system configuration
    RS485_FRAME frame = {0};
    frame.Header = RS485_FRAME_HEADER;
    frame.SlaveId = configInput->SlaveAddress; // 0x00 for broadcast
    frame.Trailer = RS485_FRAME_TRAILER;

    // Store command key and value in payload
    RS485StorePayloadKey((PRS485_PAYLOAD)frame.Payload, configInput->CommandKey);

    // Handle specific system commands
    if (strncmp(configInput->CommandKey, "S001", 4) == 0) {
        // S001: Set slave address (1-31)
        if (configInput->Value < 1 || configInput->Value > 31) {
            RS485_DEBUG_PRINT("Invalid slave address: %llu", configInput->Value);
            WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger((PRS485_PAYLOAD)frame.Payload, (UINT32)configInput->Value);
        deviceContext->CurrentSlaveAddress = (UINT8)configInput->Value;
        RS485_DEBUG_PRINT("S001: Setting slave address to %d", (UINT32)configInput->Value);
    }


    // Calculate CRC
    frame.Crc = RS485CalculateFrameCRC(&frame);

    // Store frame in uplink buffer for transmission
    status = RS485PushPayload(deviceContext->UplinkBuffer, frame.Payload);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to push system config payload to uplink buffer: 0x%08X", status);
        RS485HandleBufferOverflow(deviceContext, BufferTypeUplink, frame.Payload);
        WdfRequestComplete(request, status);
        return status;
    }

    // Update statistics
    deviceContext->HardwareStatus.TotalFramesSent++;

    // Complete the request
    WdfRequestCompleteWithInformation(request, STATUS_SUCCESS, 0);
    return STATUS_SUCCESS;
}

//
// Handle user configuration IOCTL (U001-U006 commands)
//
NTSTATUS RS485HandleUserConfigIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID inputBuffer = NULL;
    PRS485_USER_CONFIG_INPUT configInput = NULL;

    RS485_DEBUG_PRINT("Handling user configuration IOCTL");

    // Validate input buffer size
    if (inputBufferLength < sizeof(RS485_USER_CONFIG_INPUT)) {
        WdfRequestComplete(request, STATUS_BUFFER_TOO_SMALL);
        return STATUS_BUFFER_TOO_SMALL;
    }

    // Get input buffer
    status = WdfRequestRetrieveInputBuffer(request, inputBufferLength, &inputBuffer, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to retrieve input buffer: 0x%08X", status);
        WdfRequestComplete(request, status);
        return status;
    }

    configInput = (PRS485_USER_CONFIG_INPUT)inputBuffer;

    // Validate command key
    if (!RS485_IS_USER_COMMAND(configInput->CommandKey)) {
        RS485_DEBUG_PRINT("Invalid user command key: %.4s", configInput->CommandKey);
        WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
        return STATUS_INVALID_PARAMETER;
    }

    // Create frame for user configuration
    RS485_FRAME frame = {0};
    frame.Header = RS485_FRAME_HEADER;
    frame.SlaveId = configInput->SlaveAddress; // Use configured slave address
    frame.Trailer = RS485_FRAME_TRAILER;

    // Store command key in payload
    RS485StorePayloadKey((PRS485_PAYLOAD)frame.Payload, configInput->CommandKey);

    // Handle specific user commands with validation
    if (strncmp(configInput->CommandKey, "U001", 4) == 0) {
        // U001: SEL detection threshold (40-500 mA)
        if (configInput->Value < 40 || configInput->Value > 500) {
            RS485_DEBUG_PRINT("Invalid SEL threshold: %llu (valid range: 40-500)", configInput->Value);
            WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger((PRS485_PAYLOAD)frame.Payload, (UINT32)configInput->Value);
        RS485_DEBUG_PRINT("U001: Setting SEL threshold to %d mA", (UINT32)configInput->Value);
    }
    else if (strncmp(configInput->CommandKey, "U002", 4) == 0) {
        // U002: SEL maximum amplitude threshold (1000-2000 mA)
        if (configInput->Value < 1000 || configInput->Value > 2000) {
            RS485_DEBUG_PRINT("Invalid max amplitude: %llu (valid range: 1000-2000)", configInput->Value);
            WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger((PRS485_PAYLOAD)frame.Payload, (UINT32)configInput->Value);
        RS485_DEBUG_PRINT("U002: Setting max amplitude to %d mA", (UINT32)configInput->Value);
    }
    else if (strncmp(configInput->CommandKey, "U003", 4) == 0) {
        // U003: Number of SEL detections before power cycle (1-5)
        if (configInput->Value < 1 || configInput->Value > 5) {
            RS485_DEBUG_PRINT("Invalid SEL detection count: %llu (valid range: 1-5)", configInput->Value);
            WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger((PRS485_PAYLOAD)frame.Payload, (UINT32)configInput->Value);
        RS485_DEBUG_PRINT("U003: Setting SEL detection count to %d", (UINT32)configInput->Value);
    }
    else if (strncmp(configInput->CommandKey, "U004", 4) == 0) {
        // U004: Power cycle duration (200, 400, 600, 800, 1000 ms)
        UINT32 validDurations[] = {200, 400, 600, 800, 1000};
        bool validDuration = false;
        for (int i = 0; i < 5; i++) {
            if (configInput->Value == validDurations[i]) {
                validDuration = true;
                break;
            }
        }
        if (!validDuration) {
            RS485_DEBUG_PRINT("Invalid power cycle duration: %llu (valid: 200,400,600,800,1000)", configInput->Value);
            WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger((PRS485_PAYLOAD)frame.Payload, (UINT32)configInput->Value);
        RS485_DEBUG_PRINT("U004: Setting power cycle duration to %d ms", (UINT32)configInput->Value);
    }
    else if (strncmp(configInput->CommandKey, "U005", 4) == 0 || strncmp(configInput->CommandKey, "U006", 4) == 0) {
        // U005/U006: GPIO input/output functions (dual integer format)
        // Lower 32 bits: Channel ID (0 or 1)
        // Upper 32 bits: Enable/Disable flag (0 = disable, 1 = enable)
        UINT32 channel = (UINT32)(configInput->Value & 0xFFFFFFFF);
        UINT32 enableFlag = (UINT32)((configInput->Value >> 32) & 0xFFFFFFFF);

        if (channel > 1 || enableFlag > 1) {
            RS485_DEBUG_PRINT("Invalid GPIO config: channel=%d, enable=%d", channel, enableFlag);
            WdfRequestComplete(request, STATUS_INVALID_PARAMETER);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadDualIntegers((PRS485_PAYLOAD)frame.Payload, channel, enableFlag);
        RS485_DEBUG_PRINT("%s: Setting GPIO channel %d to %s",
                         configInput->CommandKey, channel, enableFlag ? "enabled" : "disabled");
    }

    // Calculate CRC
    frame.Crc = RS485CalculateFrameCRC(&frame);

    // Store frame in uplink buffer for transmission
    status = RS485PushPayload(deviceContext->UplinkBuffer, frame.Payload);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to push user config payload to uplink buffer: 0x%08X", status);
        RS485HandleBufferOverflow(deviceContext, BufferTypeUplink, frame.Payload);
        WdfRequestComplete(request, status);
        return status;
    }

    // Update statistics
    deviceContext->HardwareStatus.TotalFramesSent++;

    // Complete the request
    WdfRequestCompleteWithInformation(request, STATUS_SUCCESS, 0);
    return STATUS_SUCCESS;
}

//
// Handle receive response IOCTL
//
NTSTATUS RS485HandleReceiveResponseIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID outputBuffer = NULL;
    size_t bytesReturned = 0;

    RS485_DEBUG_PRINT("Handling receive response IOCTL");

    // Get output buffer for response data
    if (outputBufferLength < RS485_PAYLOAD_SIZE) {
        WdfRequestComplete(request, STATUS_BUFFER_TOO_SMALL);
        return STATUS_BUFFER_TOO_SMALL;
    }

    status = WdfRequestRetrieveOutputBuffer(request, outputBufferLength, &outputBuffer, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to retrieve output buffer: 0x%08X", status);
        WdfRequestComplete(request, status);
        return status;
    }

    // Check if response data is available in downlink buffer
    if (!RS485IsBufferEmpty(deviceContext->DownlinkBuffer)) {
        // Retrieve data from buffer using proper buffer functions
        status = RS485PopPayload(deviceContext->DownlinkBuffer, (UINT8*)outputBuffer);
        if (NT_SUCCESS(status)) {
            bytesReturned = RS485_PAYLOAD_SIZE;
            deviceContext->HardwareStatus.TotalFramesReceived++;
            RS485_DEBUG_PRINT("Response data retrieved from buffer");
        }
    } else {
        // No data available
        bytesReturned = 0;
        RS485_DEBUG_PRINT("No response data available");
    }

    // Complete the request
    WdfRequestCompleteWithInformation(request, status, bytesReturned);
    return status;
}

//
// Handle buffer status IOCTL
//
NTSTATUS RS485HandleBufferStatusIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID outputBuffer = NULL;
    size_t bytesReturned = 0;

    UNREFERENCED_PARAMETER(inputBufferLength);

    RS485_DEBUG_PRINT("Handling buffer status IOCTL");

    // Check output buffer size
    if (outputBufferLength < sizeof(RS485_BUFFER_STATUS)) {
        WdfRequestComplete(request, STATUS_BUFFER_TOO_SMALL);
        return STATUS_BUFFER_TOO_SMALL;
    }

    status = WdfRequestRetrieveOutputBuffer(request, outputBufferLength, &outputBuffer, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to retrieve output buffer: 0x%08X", status);
        WdfRequestComplete(request, status);
        return status;
    }

    // Fill buffer status information
    PRS485_BUFFER_STATUS bufferStatus = (PRS485_BUFFER_STATUS)outputBuffer;
    
    // Uplink buffer status (using proper pointer access)
    bufferStatus->UplinkCurrentFrames = deviceContext->UplinkBuffer->Count;
    bufferStatus->UplinkMaxFrames = deviceContext->UplinkBuffer->Capacity;
    bufferStatus->UplinkOverflowCount = 0; // Not tracked in current buffer implementation
    bufferStatus->UplinkTotalFrames = 0;   // Not tracked in current buffer implementation

    // Downlink buffer status (using proper pointer access)
    bufferStatus->DownlinkCurrentFrames = deviceContext->DownlinkBuffer->Count;
    bufferStatus->DownlinkMaxFrames = deviceContext->DownlinkBuffer->Capacity;
    bufferStatus->DownlinkOverflowCount = 0; // Not tracked in current buffer implementation
    bufferStatus->DownlinkTotalFrames = 0;   // Not tracked in current buffer implementation

    bytesReturned = sizeof(RS485_BUFFER_STATUS);

    RS485_DEBUG_PRINT("Buffer status: Uplink %u/%u, Downlink %u/%u",
                      bufferStatus->UplinkCurrentFrames, bufferStatus->UplinkMaxFrames,
                      bufferStatus->DownlinkCurrentFrames, bufferStatus->DownlinkMaxFrames);

    // Complete the request
    WdfRequestCompleteWithInformation(request, status, bytesReturned);
    return status;
}

//
// Helper function to calculate frame CRC (simplified implementation)
//
UINT16 RS485CalculateFrameCRC(const PRS485_FRAME frame)
{
    if (frame == NULL) {
        return 0;
    }

    // Simplified CRC calculation - in real implementation, use proper CRC16
    UINT16 crc = 0xFFFF;
    const UINT8* data = (const UINT8*)frame;
    
    // Calculate CRC over header, ID, and payload (exclude CRC and trailer fields)
    for (UINT32 i = 0; i < (sizeof(RS485_FRAME) - sizeof(UINT16) - sizeof(UINT8)); i++) {
        crc ^= data[i];
        for (UINT32 j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    
    return crc;
}
