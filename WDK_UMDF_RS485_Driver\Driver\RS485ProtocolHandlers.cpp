//
// RS485ProtocolHandlers.cpp - Protocol frame processing functions
// RS485 UMDF 2.0 Driver for AI-SLDAP Communication
//

#include "RS485FilterDriver.h"

//
// Forward declarations for functions defined later in this file
//
NTSTATUS RS485ProcessSystemConfigFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame);
NTSTATUS RS485ProcessUserConfigFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame);
NTSTATUS RS485ProcessDataQueryFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame);
NTSTATUS RS485ProcessModelDataFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame);

//
// Route frame to appropriate API category handler
//
NTSTATUS RS485RouteFrameToAPICategory(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;

    // Extract command from payload (first 4 bytes)
    char command[5] = {0};
    RtlCopyMemory(command, Frame->Payload, 4);

    RS485_DEBUG_PRINT("Processing frame with command: %s", command);

    // Route based on command prefix
    if (command[0] == 'S') {
        // System configuration commands (S001, S002, etc.)
        status = RS485ProcessSystemConfigFrame(DeviceContext, Frame);
    }
    else if (command[0] == 'U') {
        // User configuration commands (U001, U002, etc.)
        status = RS485ProcessUserConfigFrame(DeviceContext, Frame);
    }
    else if (command[0] == 'A') {
        // Data query commands (A001, A002, etc.)
        status = RS485ProcessDataQueryFrame(DeviceContext, Frame);
    }
    else if (command[0] == 'W') {
        // Model data commands (W001, W002, etc.)
        status = RS485ProcessModelDataFrame(DeviceContext, Frame);
    }
    else {
        RS485_DEBUG_PRINT("Unknown command prefix: %c", command[0]);
        status = STATUS_INVALID_PARAMETER;
        RS485HandleFrameError(DeviceContext, Frame, RS485_INVALID_FUNCTION_CODE);
    }

    return status;
}

//
// Process assign data frame (Master -> Slave configuration)
//
NTSTATUS RS485ProcessAssignDataFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing assign data frame");

    // Validate frame format
    if (Frame->Header != RS485_FRAME_HEADER || Frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(DeviceContext, Frame, RS485_FRAME_SYNC_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Route to appropriate handler based on command
    return RS485RouteFrameToAPICategory(DeviceContext, Frame);
}

//
// Process request data frame (Master -> Slave data request)
//
NTSTATUS RS485ProcessRequestDataFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing request data frame");

    // Validate frame format
    if (Frame->Header != RS485_FRAME_HEADER || Frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(DeviceContext, Frame, RS485_FRAME_SYNC_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Store request in pending queue for response tracking
    // This is a simplified implementation - full version would manage request queue
    DeviceContext->PendingRequests++;

    // Route to appropriate handler
    return RS485RouteFrameToAPICategory(DeviceContext, Frame);
}

//
// Process response frame (Slave -> Master response)
//
NTSTATUS RS485ProcessResponseFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing response frame");

    // Validate frame format
    if (Frame->Header != RS485_FRAME_HEADER || Frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(DeviceContext, Frame, RS485_FRAME_SYNC_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Store response data in downlink buffer using proper buffer functions
    NTSTATUS status = RS485PushPayload(DeviceContext->DownlinkBuffer, Frame->Payload);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to push payload to downlink buffer: 0x%08X", status);
        RS485HandleBufferOverflow(DeviceContext, BufferTypeDownlink, Frame->Payload);
        return status;
    }

    // Update response statistics
    DeviceContext->PendingRequests = (DeviceContext->PendingRequests > 0) ?
                                     DeviceContext->PendingRequests - 1 : 0;

    // Use GetSystemTimeAsFileTime for UMDF (not KeQuerySystemTime which is kernel-mode)
    FILETIME fileTime;
    GetSystemTimeAsFileTime(&fileTime);
    DeviceContext->HardwareStatus.LastResponseTime.QuadPart =
        ((LARGE_INTEGER*)&fileTime)->QuadPart;

    return STATUS_SUCCESS;
}

//
// Process resend request frame (bidirectional error recovery)
//
NTSTATUS RS485ProcessResendRequestFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing resend request frame");

    // Validate frame format
    if (Frame->Header != RS485_FRAME_HEADER || Frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(DeviceContext, Frame, RS485_FRAME_SYNC_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Extract sequence number or frame ID from payload for resend identification
    UINT32 resendId = *(UINT32*)Frame->Payload;

    RS485_DEBUG_PRINT("Resend requested for frame ID: %u", resendId);

    // Look up frame in transmission history and resend if found
    // This is a simplified implementation - full version would maintain transmission history
    DeviceContext->ErrorStatistics.RetransmissionRequests++;

    return STATUS_SUCCESS;
}

//
// Helper function to store frame in buffer
//
NTSTATUS RS485StoreFrameInBuffer(PRS485_BUFFER Buffer, const RS485_FRAME* Frame)
{
    if (Buffer == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Check if buffer has space (simplified check)
    if (Buffer->CurrentFrameCount >= Buffer->MaxFrameCount) {
        Buffer->OverflowCount++;
        return STATUS_BUFFER_OVERFLOW;
    }

    // Store payload data (12 bytes) in buffer
    UINT32 writeIndex = Buffer->WriteIndex;
    RtlCopyMemory(&Buffer->Data[writeIndex * RS485_PAYLOAD_SIZE],
                  Frame->Payload,
                  RS485_PAYLOAD_SIZE);

    // Update buffer indices
    Buffer->WriteIndex = (writeIndex + 1) % Buffer->MaxFrameCount;
    Buffer->CurrentFrameCount++;
    Buffer->TotalFramesStored++;

    return STATUS_SUCCESS;
}

//
// Complete implementations for specific command handlers
//

//
// Process System Configuration Commands (S-Series)
//
NTSTATUS RS485ProcessSystemConfigFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Extract command from payload (first 4 bytes)
    char command[5] = {0};
    RtlCopyMemory(command, Frame->Payload, 4);

    // Extract value from payload (bytes 4-7 as 32-bit integer, little-endian)
    UINT32 value = *(UINT32*)&Frame->Payload[4];

    RS485_DEBUG_PRINT("Processing system config command: %s, value: %u", command, value);

    NTSTATUS status = STATUS_SUCCESS;

    if (strncmp(command, "S001", 4) == 0) {
        // S001: Set RS485 slave address (1-31)
        if (value >= 1 && value <= 31) {
            DeviceContext->CurrentSlaveAddress = (UINT8)value;
            RS485_DEBUG_PRINT("S001: Slave address set to %u", value);

            // Create acknowledgment frame
            RS485_FRAME ackFrame = {0};
            ackFrame.Header = RS485_FRAME_HEADER;
            ackFrame.SlaveId = (UINT8)value; // Use the new address
            ackFrame.Trailer = RS485_FRAME_TRAILER;

            // Copy command back in payload
            RtlCopyMemory(ackFrame.Payload, command, 4);
            *(UINT32*)&ackFrame.Payload[4] = value; // Confirm the value

            // Store acknowledgment in downlink buffer
            status = RS485PushPayload(DeviceContext->DownlinkBuffer, ackFrame.Payload);
        } else {
            RS485_DEBUG_PRINT("S001: Invalid slave address %u (must be 1-31)", value);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else if (strncmp(command, "S002", 4) == 0) {
        // S002: Set baud rate - FIXED AT 9600 (requirement)
        // Note: Baud rate is fixed at 9600 and should not be modifiable
        if (value == 9600) {
            RS485_DEBUG_PRINT("S002: Baud rate confirmed at fixed 9600 bps");

            // Create acknowledgment frame
            RS485_FRAME ackFrame = {0};
            ackFrame.Header = RS485_FRAME_HEADER;
            ackFrame.SlaveId = DeviceContext->CurrentSlaveAddress;
            ackFrame.Trailer = RS485_FRAME_TRAILER;

            // Copy command back in payload
            RtlCopyMemory(ackFrame.Payload, command, 4);
            *(UINT32*)&ackFrame.Payload[4] = 9600; // Always return 9600

            // Store acknowledgment in downlink buffer
            status = RS485PushPayload(DeviceContext->DownlinkBuffer, ackFrame.Payload);
        } else {
            RS485_DEBUG_PRINT("S002: Baud rate is fixed at 9600 bps, cannot change to %u", value);
            // Still acknowledge but return fixed value
            RS485_FRAME ackFrame = {0};
            ackFrame.Header = RS485_FRAME_HEADER;
            ackFrame.SlaveId = DeviceContext->CurrentSlaveAddress;
            ackFrame.Trailer = RS485_FRAME_TRAILER;

            RtlCopyMemory(ackFrame.Payload, command, 4);
            *(UINT32*)&ackFrame.Payload[4] = 9600; // Always return 9600

            status = RS485PushPayload(DeviceContext->DownlinkBuffer, ackFrame.Payload);
        }
    }
    else {
        RS485_DEBUG_PRINT("Unknown system command: %s", command);
        status = STATUS_INVALID_PARAMETER;
    }

    return status;
}

//
// Process User Configuration Commands (U-Series)
//
NTSTATUS RS485ProcessUserConfigFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Extract command from payload (first 4 bytes)
    char command[5] = {0};
    RtlCopyMemory(command, Frame->Payload, 4);

    RS485_DEBUG_PRINT("Processing user config command: %s", command);

    NTSTATUS status = STATUS_SUCCESS;
    RS485_FRAME ackFrame = {0};
    ackFrame.Header = RS485_FRAME_HEADER;
    ackFrame.SlaveId = DeviceContext->CurrentSlaveAddress;
    ackFrame.Trailer = RS485_FRAME_TRAILER;
    RtlCopyMemory(ackFrame.Payload, command, 4);

    if (strncmp(command, "U001", 4) == 0) {
        // U001: SEL detection threshold (40-500 mA)
        UINT32 threshold = *(UINT32*)&Frame->Payload[4];
        if (threshold >= 40 && threshold <= 500) {
            RS485_DEBUG_PRINT("U001: SEL threshold set to %u mA", threshold);
            *(UINT32*)&ackFrame.Payload[4] = threshold;
        } else {
            RS485_DEBUG_PRINT("U001: Invalid threshold %u (must be 40-500)", threshold);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else if (strncmp(command, "U002", 4) == 0) {
        // U002: SEL maximum amplitude threshold (1000-2000 mA)
        UINT32 maxAmplitude = *(UINT32*)&Frame->Payload[4];
        if (maxAmplitude >= 1000 && maxAmplitude <= 2000) {
            RS485_DEBUG_PRINT("U002: Max amplitude set to %u mA", maxAmplitude);
            *(UINT32*)&ackFrame.Payload[4] = maxAmplitude;
        } else {
            RS485_DEBUG_PRINT("U002: Invalid max amplitude %u (must be 1000-2000)", maxAmplitude);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else if (strncmp(command, "U003", 4) == 0) {
        // U003: SEL detection count before power cycle (1-5)
        UINT32 detectionCount = *(UINT32*)&Frame->Payload[4];
        if (detectionCount >= 1 && detectionCount <= 5) {
            RS485_DEBUG_PRINT("U003: Detection count set to %u", detectionCount);
            *(UINT32*)&ackFrame.Payload[4] = detectionCount;
        } else {
            RS485_DEBUG_PRINT("U003: Invalid detection count %u (must be 1-5)", detectionCount);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else if (strncmp(command, "U004", 4) == 0) {
        // U004: Power cycle duration (200,400,600,800,1000 ms)
        UINT32 duration = *(UINT32*)&Frame->Payload[4];
        if (duration == 200 || duration == 400 || duration == 600 ||
            duration == 800 || duration == 1000) {
            RS485_DEBUG_PRINT("U004: Power cycle duration set to %u ms", duration);
            *(UINT32*)&ackFrame.Payload[4] = duration;
        } else {
            RS485_DEBUG_PRINT("U004: Invalid duration %u (must be 200,400,600,800,1000)", duration);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else if (strncmp(command, "U005", 4) == 0) {
        // U005: GPIO input channel control (Channel + Enable flag)
        UINT64 gpioValue = *(UINT64*)&Frame->Payload[4];
        UINT32 channel = (UINT32)(gpioValue & 0xFFFFFFFF);
        UINT32 enable = (UINT32)((gpioValue >> 32) & 0xFFFFFFFF);

        if (channel <= 1 && (enable == 0 || enable == 1)) {
            RS485_DEBUG_PRINT("U005: GPIO input ch%u %s", channel, enable ? "enabled" : "disabled");
            *(UINT64*)&ackFrame.Payload[4] = gpioValue;
        } else {
            RS485_DEBUG_PRINT("U005: Invalid GPIO config - channel:%u, enable:%u", channel, enable);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else if (strncmp(command, "U006", 4) == 0) {
        // U006: GPIO output channel control (Channel + Enable flag)
        UINT64 gpioValue = *(UINT64*)&Frame->Payload[4];
        UINT32 channel = (UINT32)(gpioValue & 0xFFFFFFFF);
        UINT32 enable = (UINT32)((gpioValue >> 32) & 0xFFFFFFFF);

        if (channel <= 1 && (enable == 0 || enable == 1)) {
            RS485_DEBUG_PRINT("U006: GPIO output ch%u %s", channel, enable ? "enabled" : "disabled");
            *(UINT64*)&ackFrame.Payload[4] = gpioValue;
        } else {
            RS485_DEBUG_PRINT("U006: Invalid GPIO config - channel:%u, enable:%u", channel, enable);
            status = STATUS_INVALID_PARAMETER;
        }
    }
    else {
        RS485_DEBUG_PRINT("Unknown user command: %s", command);
        status = STATUS_INVALID_PARAMETER;
    }

    // Send acknowledgment if command was valid
    if (NT_SUCCESS(status)) {
        status = RS485PushPayload(DeviceContext->DownlinkBuffer, ackFrame.Payload);
    }

    return status;
}

//
// Process Data Query Commands (A-Series)
//
NTSTATUS RS485ProcessDataQueryFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Extract command from payload (first 4 bytes)
    char command[5] = {0};
    RtlCopyMemory(command, Frame->Payload, 4);

    RS485_DEBUG_PRINT("Processing data query command: %s", command);

    NTSTATUS status = STATUS_SUCCESS;
    RS485_FRAME responseFrame = {0};
    responseFrame.Header = RS485_FRAME_HEADER;
    responseFrame.SlaveId = DeviceContext->CurrentSlaveAddress;
    responseFrame.Trailer = RS485_FRAME_TRAILER;
    RtlCopyMemory(responseFrame.Payload, command, 4);

    if (strncmp(command, "A001", 4) == 0) {
        // A001: Request SEL event log (JSON structure)
        RS485_DEBUG_PRINT("A001: SEL event log requested");
        // Simulate JSON data pointer (in real implementation, this would point to actual data)
        UINT64 jsonPointer = 0x12345678ABCDEF00ULL;
        *(UINT64*)&responseFrame.Payload[4] = jsonPointer;
    }
    else if (strncmp(command, "A002", 4) == 0) {
        // A002: Request device status (16-bit flags)
        RS485_DEBUG_PRINT("A002: Device status requested");
        // Simulate device status flags
        UINT32 statusFlags = 0x00000001; // Device active
        *(UINT32*)&responseFrame.Payload[4] = statusFlags;
    }
    else if (strncmp(command, "A003", 4) == 0) {
        // A003: Request firmware version
        RS485_DEBUG_PRINT("A003: Firmware version requested");
        // Simulate firmware version (e.g., version 1.2.3 = 0x010203)
        UINT32 firmwareVersion = 0x010203;
        *(UINT32*)&responseFrame.Payload[4] = firmwareVersion;
    }
    else if (strncmp(command, "A004", 4) == 0) {
        // A004: Request system statistics (JSON structure)
        RS485_DEBUG_PRINT("A004: System statistics requested");
        // Simulate JSON data pointer
        UINT64 statsPointer = 0x87654321FEDCBA00ULL;
        *(UINT64*)&responseFrame.Payload[4] = statsPointer;
    }
    else if (strncmp(command, "A005", 4) == 0) {
        // A005: Request current configuration (JSON structure)
        RS485_DEBUG_PRINT("A005: Current configuration requested");
        // Simulate JSON data pointer
        UINT64 configPointer = 0xAABBCCDDEEFF0000ULL;
        *(UINT64*)&responseFrame.Payload[4] = configPointer;
    }
    else {
        RS485_DEBUG_PRINT("Unknown data query command: %s", command);
        status = STATUS_INVALID_PARAMETER;
    }

    // Send response if command was valid
    if (NT_SUCCESS(status)) {
        status = RS485PushPayload(DeviceContext->DownlinkBuffer, responseFrame.Payload);
    }

    return status;
}

//
// Process Model Data Commands (W-Series)
//
NTSTATUS RS485ProcessModelDataFrame(PRS485_DEVICE_CONTEXT DeviceContext, const RS485_FRAME* Frame)
{
    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Extract command from payload (first 4 bytes)
    char command[5] = {0};
    RtlCopyMemory(command, Frame->Payload, 4);

    RS485_DEBUG_PRINT("Processing model data command: %s", command);

    NTSTATUS status = STATUS_SUCCESS;
    RS485_FRAME responseFrame = {0};
    responseFrame.Header = RS485_FRAME_HEADER;
    responseFrame.SlaveId = DeviceContext->CurrentSlaveAddress;
    responseFrame.Trailer = RS485_FRAME_TRAILER;
    RtlCopyMemory(responseFrame.Payload, command, 4);

    if (strncmp(command, "W001", 4) == 0) {
        // W001: Write model data to FRAM
        RS485_DEBUG_PRINT("W001: Write model data to FRAM");
        // Extract data from payload (bytes 4-11)
        // In real implementation, this would write to FRAM
        // For now, just acknowledge the write operation
        *(UINT32*)&responseFrame.Payload[4] = 1; // Success indicator
    }
    else if (strncmp(command, "W002", 4) == 0) {
        // W002: Read model data from FRAM
        RS485_DEBUG_PRINT("W002: Read model data from FRAM");
        // In real implementation, this would read from FRAM
        // For now, return simulated model data
        FLOAT modelData = 3.14159f; // Simulated model weight/bias
        *(FLOAT*)&responseFrame.Payload[4] = modelData;
    }
    else {
        RS485_DEBUG_PRINT("Unknown model data command: %s", command);
        status = STATUS_INVALID_PARAMETER;
    }

    // Send response if command was valid
    if (NT_SUCCESS(status)) {
        status = RS485PushPayload(DeviceContext->DownlinkBuffer, responseFrame.Payload);
    }

    return status;
}
